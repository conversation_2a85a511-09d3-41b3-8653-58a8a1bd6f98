<template>
  <q-table
    :rows="filteredRows"
    :columns="evaluateManagementColumns"
    row-key="id"
    :pagination="pagination"
    :rows-per-page-options="[5, 10, 15]"
    flat
    bordered
    separator="cell"
  >
    <!-- Date Column -->
    <template v-slot:body-cell-date="{ row }">
      <q-td class="text-center">{{ row.date }}</q-td>
    </template>

    <!-- Link Column -->
    <template v-slot:body-cell-link="{ row }">
      <q-td class="text-center">
        <div class="flex justify-center">
          <q-icon name="link" size="20px" class="cursor-pointer" @click="openShareDialog(row)" />
        </div>
      </q-td>
    </template>

    <!-- Actions Column -->
    <template v-slot:body-cell-actions="{ row }">
      <q-td class="text-center">
        <div class="q-gutter-x-sm flex justify-center">
          <q-btn
            dense
            unelevated
            class="view-icon"
            icon="visibility"
            @click="onClickPreview(row)"
          />
          <q-btn dense unelevated class="edit-graph-icon" icon="edit" @click="onClickEdit(row)" />
          <q-btn
            dense
            unelevated
            class="edit-graph-icon"
            icon="bar_chart"
            @click="onClickChart(row)"
          />
          <q-btn dense unelevated class="del-icon" icon="delete" @click="deleteItem(row)" />
          <q-btn dense unelevated class="view-icon" icon="visibility" @click="viewItem(row)" />
        </div>
      </q-td>
    </template>
  </q-table>
  <ShareLinkDialog ref="shareLinkDialog" />
  <ConfirmDialog
    v-model="confirmDialogVisible"
    :title="titleDialog"
    @confirm="onConfirmDelete"
    @cancel="onCancelDelete"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import ShareLinkDialog from 'src/components/common/ShareLinkDialog.vue';
import router from 'src/router';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { Assessment } from 'src/types/models';
import { evaluateManagementColumns } from 'src/data/table_columns';
import ConfirmDialog from '../ConfirmDialog.vue';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { QTableProps } from 'quasar';
const shareLinkDialog = ref<InstanceType<typeof ShareLinkDialog> | null>(null);
const formData = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');
function openShareDialog(row: Assessment) {
  if (shareLinkDialog.value) {
    shareLinkDialog.value.openDialog(`${row.linkURL}`, 'ตกลง');
  }
}
const rows = ref<Assessment[]>([]);

const filter = ref('');
const pagination = ref({ ...defaultPaginationValue });

const filteredRows = computed(() => {
  if (!filter.value) return formData.value;
  const keyword = filter.value.toLowerCase();
  return rows.value.filter(
    (row) =>
      row.name.toLowerCase().includes(keyword) ||
      row.creatorUser?.name.toLowerCase().includes(keyword),
  );
});
// stub functions
async function onClickPreview(row: Assessment) {
  await router.push({
    name: 'evaluate-preview',
    params: {
      id: row.id,
      status: 'preview',
    },
  });
}
async function viewItem(row: Assessment) {
  await router.push({
    name: 'evaluate-do',
    params: {
      id: row.id,
      status: 'do',
    },
  });
}

async function onClickEdit(row: Assessment) {
  try {
    const resolveRoute = router.resolve({
      name: 'evaluate-edit',
      params: { id: row.id.toString() },
      hash: '#questions',
    });
    await router.push(resolveRoute);
  } catch (error) {
    console.error('Navigation to edit view failed:', error);
    return;
  }
}

async function onClickChart(row: Assessment) {
  try {
    const resolveRoute = router.resolve({
      name: 'evaluate-edit',
      params: { id: row.id.toString() },
      hash: '#replies',
    });
    await router.push(resolveRoute);
    console.log('ดูกราฟ', row);
  } catch (error) {
    console.error('Navigation to graph view failed:', error);
    return;
  }
}

function deleteItem(row: Assessment) {
  selectedRowToDelete.value = row;
  titleDialog.value = `ยืนยันการลบแบบประเมิน ${row.name}`;
  confirmDialogVisible.value = true;
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService('evaluate').deleteOne(selectedRowToDelete.value.id);
    // await form.deleteForm(selectedRowToDelete.value.id);
    await fetchData(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

async function fetchData(pagination: QTableProps['pagination']) {
  const res = await new AssessmentService('evaluate').fetchAll(pagination);
  formData.value = res.data;
}

onMounted(async () => {
  await fetchData(pagination.value);
});
</script>

<style scoped>
.view-icon {
  background-color: #39303d;
  color: white;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: white;
  border-radius: 12px;
}

.del-icon {
  background-color: #ab2433;
  color: white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>
