<template>
  <div class="q-ml-md">
    <!-- รายการตัวเลือก (Radio Input หน้า Text Field) -->
    <div
      v-for="(option, index) in store.radioOptions"
      :key="index"
      class="row items-center q-mb-sm draggable-row"
      @dragover.prevent
      @drop="store.drop(index, $event)"
      :style="{ transition: 'all 0.3s ease', opacity: store.draggedIndex === index ? 0.5 : 1 }"
    >
      <q-btn
        flat
        round
        icon="drag_indicator"
        color="grey"
        @mousedown="store.startDrag(index)"
        draggable="true"
        @dragstart="store.handleDragStart($event)"
        @dragend="store.endDrag"
        @mouseover="store.hoverRow(index)"
        style="cursor: move"
      />
      <q-radio
        v-model="store.checkboxOptions"
        :val="store.radioOptions[index]?.value"
        color="primary"
        disable
        class="q-mr-sm"
      />
      <div class="row items-center">
        <q-input
          v-model="store.radioOptions[index]!.optionText"
          :placeholder="store.radioOptions[index]!.placeholder"
          dense
          @input="handleOptionTextChange(index, $event)"
          class="q-mr-sm"
        />
        <q-input
          v-model.number="store.radioOptions[index]!.score"
          placeholder="คะแนน"
          type="number"
          dense
          style="max-width: 80px"
          @input="handleOptionScoreChange(index, $event)"
        />
      </div>
      <q-btn flat round icon="image" color="grey" class="q-ml-sm" />
      <q-btn
        v-if="store.radioOptions.length > 1"
        flat
        round
        icon="close"
        @click="store.removeOption(index)"
        :disable="store.radioOptions.length <= 1"
        class="q-ml-sm"
      />
    </div>

    <!-- ปุ่มเพิ่มช้อย -->
    <div class="row items-center q-mt-sm">
      <q-btn flat color="secondary" label="เพิ่มตัวเลือก" icon="add" @click="store.addOption()" />
      <q-btn
        flat
        color="secondary"
        label="เพิ่ม 'อื่นๆ'"
        icon="add"
        @click="store.addOtherOption()"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject } from 'vue';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

// Inject the store from the parent component
const store = inject<ItemBlockStore>('blockStore');

// Inject auto-save functions from parent ItemBlockComponent
const autoSave = inject<{
  triggerOptionAutoSave: (
    optionId: number,
    field: 'optionText' | 'score',
    value: string | number,
  ) => void;
  isSaving: { value: boolean };
}>('autoSave');

// Make sure the store is available
if (!store) {
  console.error('ItemBlockStore not provided to ChoiceAns component');
  throw new Error('ItemBlockStore not provided to ChoiceAns component');
}

// Get the itemBlock prop to access actual option IDs
const props = defineProps<{
  itemBlock: ItemBlock;
}>();

// Handler for option text changes with auto-save
const handleOptionTextChange = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = target.value;

  // Update the store
  store.updateOption(index);

  // Trigger auto-save if we have the option ID and auto-save is available
  // Get the actual option ID from the itemBlock prop
  const actualOption = props.itemBlock.options?.[index];
  if (actualOption && autoSave && actualOption.id) {
    autoSave.triggerOptionAutoSave(actualOption.id, 'optionText', newValue);
  }
};

// Handler for option score changes with auto-save
const handleOptionScoreChange = (index: number, event: Event) => {
  const target = event.target as HTMLInputElement;
  const newValue = Number(target.value) || 0;

  // Update the store
  store.updateOption(index);

  // Trigger auto-save if we have the option ID and auto-save is available
  // Get the actual option ID from the itemBlock prop
  const actualOption = props.itemBlock.options?.[index];
  if (actualOption && autoSave && actualOption.id) {
    autoSave.triggerOptionAutoSave(actualOption.id, 'score', newValue);
  }
};
</script>

<style scoped>
.q-input {
  max-width: 400px;
  width: 400px;
  margin-right: 10px;
}
.draggable-row {
  transition: all 0.3s ease;
}

.draggable-row:hover,
.draggable-row[dragged-index]:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.draggable-row.dragging {
  opacity: 0.5;
}
</style>
