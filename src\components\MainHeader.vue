<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import utilsConfigs from 'src/configs/utilsConfigs';
import type { User } from 'src/types/models';
import { useGlobalStore } from 'src/stores/global';
import { useCreateItemEvaluateFormStore } from 'src/stores/evaluate/formItemCreateStore';
import EvaluateDuplicate from './evaluate/EvaluateDuplicate.vue';
import { api } from 'src/boot/axios';
import { useAssessmentStore } from 'src/stores/asm';

const asmStore = useAssessmentStore();
const authStore = useAuthStore();
const route = useRoute();
const globalStore = useGlobalStore();
const router = useRouter();
// const showEvaluateDuplicate = ref(false);
const createItemFormStore = useCreateItemEvaluateFormStore();

const user = ref<User>();
const editableTitle = ref<string>(''); // ตัวแปรที่ใช้กับ q-input
const isSavingTitle = ref(false); // แสดงสถานะการบันทึก

// Use name from assessment in store (since 'title' does not exist, use 'name')
const asmTitle = computed({
  get: () => asmStore.currentAssessment?.name || '',
  set: (val: string) => {
    if (asmStore.currentAssessment) {
      asmStore.currentAssessment.name = val;
    }
  },
});

// debounce สำหรับการ save ชื่อแบบฟอร์ม
const saveTitle = async (newTitle: string) => {
  try {
    isSavingTitle.value = true;
    await api.patch(`assessments/${route.params.id as string}`, {
      name: newTitle,
    });
    asmTitle.value = newTitle;
  } catch (error) {
    console.error('Error saving quiz title:', error);
  } finally {
    isSavingTitle.value = false;
  }
};

// // watcher สำหรับ autosave
// watch(editableTitle, (newTitle) => {
//   if (isAssessment.value && newTitle.trim()) {
//     void saveTitle(newTitle.trim());
//   }
// });

const headerTitle = computed(() => {
  if (route.name === 'quiz-edit' && route.params.id) {
    if (asmTitle.value) {
      return asmTitle.value;
    }
    return `แบบทดสอบ #${String(route.params.id)}`;
  }
  if (route.path.includes('/quiz')) {
    return 'ระบบแบบทดสอบ';
  }
  if (route.path.includes('/evaluate')) {
    return 'ระบบแบบสอบถาม';
  }
  if (route.path.includes('/ums')) {
    return 'จัดการสิทธิ์ในระบบ';
  }
  return 'ระบบจัดการ';
});

const isAssessment = computed(() => {
  return route.name === 'quiz-edit' || route.name === 'evaluate-edit';
});

const isQuizEditor = computed(() => {
  return route.name === 'quiz-edit';
});

const isEvaluateEditor = computed(() => {
  return route.name === 'evaluate-edit';
});

// ฟังก์ชันสำหรับดึง quiz title
const fetchQuizTitle = (quizId: string) => {
  try {
    // Use name from assessment in store
    const title = asmStore.currentAssessment?.name || '';
    asmTitle.value = title;
    editableTitle.value = title;
  } catch (error) {
    console.error('Error fetching quiz title:', error);
    const fallback = `แบบทดสอบ #${quizId}`;
    asmTitle.value = fallback;
    editableTitle.value = fallback;
  }
};

// ฟังก์ชันสำหรับ Evaluate Status Bar
const onClickLink = () => {
  console.log('Link clicked');
};

const onClickPreview = async () => {
  if (isQuizEditor.value) {
    await router.push({ name: 'quiz-preview', params: { id: route.params.id } });
    return;
  }
  if (isEvaluateEditor.value) {
    await router.push({ name: 'evaluate-preview', params: { id: route.params.id } });
    return;
  }
};

const onClickPublish = () => {
  console.log('Publish clicked');
};

const onDuplicate = () => {
  createItemFormStore.dupicateDialog = true;
  console.log('Duplicate', createItemFormStore.dupicateDialog);
};

watch(
  () => route.params.id,
  (newId) => {
    if (route.name === 'quiz-id' && newId) {
      fetchQuizTitle(newId as string);
    } else {
      asmTitle.value = '';
      editableTitle.value = '';
    }
  },
  { immediate: true },
);

function onDuplicateConfirm(id: number) {
  console.log(id);
  // console.log(evaluateFormStore.currentAssessment!.id);

  // if (evaluateFormStore.currentAssessment?.id)
  //   await new AssessmentService('evaluate').duplicate(id, evaluateFormStore.currentAssessment.id);
}

const isHomePage = computed(() => {
  return route.name === 'home';
});

// Debounced save on blur
const onTitleBlur = async () => {
  if (isAssessment.value && asmTitle.value.trim()) {
    await saveTitle(asmTitle.value.trim());
  }
};

// Watch for assessment load and update asmTitle
watch(
  () => asmStore.currentAssessment,
  (assessment) => {
    if (assessment && assessment.name) {
      asmTitle.value = assessment.name;
    }
  },
  { immediate: true },
);

onMounted(() => {
  globalStore.Loading();
  user.value = authStore.getCurrentUser();
  if (route.name === 'quiz-id' && route.params.id) {
    fetchQuizTitle(route.params.id as string);
  }
});
</script>

<template>
  <q-header>
    <q-toolbar class="text-black justify-between">
      <!-- Left Section -->
      <q-toolbar-title class="flex items-center q-gutter-x-sm">
        <q-btn
          v-if="!isHomePage"
          padding="xs"
          style="background: transparent"
          icon="menu"
          color="white"
          flat
          @click="globalStore.toggleLeftDrawer()"
        ></q-btn>
        <img src="/brand/brand-white.png" alt="brand" width="40px" height="40px" />

        <div
          v-if="!isHomePage"
          class="text-white text-h6 q-ml-md cursor-pointer header-title-hover"
        >
          <q-input
            v-if="isAssessment"
            v-model="asmTitle"
            dense
            color="white"
            input-class="text-white"
            borderless
            class="bg-transparent header-title-input"
            @blur="onTitleBlur"
          >
            <template v-slot:append>
              <q-spinner-dots v-if="isSavingTitle" size="sm" color="white" />
            </template>
          </q-input>

          <div v-else>
            {{ headerTitle }}
          </div>
        </div>

        <div v-if="isAssessment && isSavingTitle" class="saving-container">
          <div class="saving-box">
            <span class="saving-text">กำลังบันทึก</span>
            <span class="animated-ellipsis">
              <span class="ellipsis-dot">.</span>
              <span class="ellipsis-dot">.</span>
              <span class="ellipsis-dot">.</span>
            </span>
          </div>
        </div>
      </q-toolbar-title>

      <!-- Right Section -->
      <div class="row items-center q-gutter-sm">
        <!-- Evaluate Action Buttons - แสดงเฉพาะในหน้า evaluate -->
        <div v-if="isAssessment" class="evaluate-buttons-container q-mr-md">
          <q-btn
            flat
            round
            icon="upload_file"
            size="md"
            class="text-white import-file-btn"
            @click="onDuplicate"
          >
            <q-tooltip class="bg-dark">คัดลอกฟอร์ม</q-tooltip></q-btn
          >
          <q-btn
            flat
            round
            icon="link"
            size="md"
            class="text-white evaluate-icon-btn"
            @click="onClickLink"
            ><q-tooltip class="bg-dark">คัดลอกลิงค์</q-tooltip></q-btn
          >
          <q-btn
            flat
            round
            icon="visibility"
            size="md"
            class="text-white evaluate-icon-btn"
            @click="onClickPreview"
            ><q-tooltip class="bg-dark">ดูพรีวิว</q-tooltip></q-btn
          >
          <q-btn
            unelevated
            label="เผยแพร่"
            color="yellow-8"
            @click="onClickPublish"
            class="publish-btn-improved"
          ></q-btn>
        </div>

        <!-- User Section -->
        <div class="cursor-pointer row">
          <div class="text-white q-mr-sm q-my-auto text-caption">
            <div>{{ user?.name }}</div>
            <div>{{ user?.roles?.[0]?.name }}</div>
          </div>
          <q-avatar size="40px" class="q-mx-xs">
            <img draggable="false" v-if="user" src="/mockup/avatar.webp" />
            <q-icon v-else name="account_circle" size="40px" />
          </q-avatar>

          <q-menu anchor="bottom right" self="top right">
            <q-card style="min-width: 200px">
              <q-list bordered separator>
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="account_circle" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ user?.name }}</q-item-label>
                    <q-item-label caption v-for="(sys, index) in utilsConfigs.systems" :key="index">
                      • {{ sys.sysNameTh }}
                    </q-item-label>
                  </q-item-section>
                </q-item>

                <q-item clickable v-ripple @click="authStore.logout()">
                  <q-item-section avatar>
                    <q-icon name="logout" color="red" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="text-red">ออกจากระบบ </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card>
          </q-menu>
        </div>
      </div>
    </q-toolbar>
  </q-header>
  <EvaluateDuplicate @confirm="onDuplicateConfirm" />
</template>

<style scoped>
.header-title-hover {
  transition: opacity 0.2s ease;
  user-select: none;
}

.header-title-hover:hover {
  opacity: 0.8;
  cursor: pointer;
}

.saving-container {
  position: relative;
  margin-left: 12px;
}

.saving-box {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.saving-text {
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1;
}

.animated-ellipsis {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}

.animated-ellipsis .ellipsis-dot {
  color: white;
  font-size: 0.75em;
  font-weight: bold;
  opacity: 0.8;
  animation-name: dot-wave-animation;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}

.animated-ellipsis .ellipsis-dot:nth-child(1) {
  animation-delay: 0s;
}
.animated-ellipsis .ellipsis-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.animated-ellipsis .ellipsis-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-wave-animation {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* Styles สำหรับ Evaluate Buttons */
.evaluate-buttons-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;
}

.evaluate-icon-btn {
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.evaluate-icon-btn .q-btn__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.evaluate-icon-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.publish-btn-improved {
  height: 40px !important;
  min-width: 100px;
  padding: 0 20px;
  color: black !important;
  font-weight: 500;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.publish-btn-improved .q-btn__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.publish-btn-improved:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

@media (max-width: 768px) {
  .q-toolbar {
    flex-wrap: wrap;
  }

  .q-toolbar-title {
    width: 100%;
    margin-bottom: 8px;
  }

  .evaluate-buttons-container {
    gap: 6px;
  }

  .evaluate-icon-btn {
    width: 36px !important;
    height: 36px !important;
  }

  .publish-btn-improved {
    height: 36px !important;
    min-width: 80px;
    font-size: 0.8rem;
  }
}
</style>
